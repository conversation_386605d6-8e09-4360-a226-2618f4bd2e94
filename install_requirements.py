#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Install all required packages"""
    print("=" * 50)
    print("    تثبيت متطلبات نظام إدارة المستودعات")
    print("=" * 50)
    print()
    
    # List of required packages
    packages = [
        "PyQt6==6.6.1",
        "reportlab==4.0.7", 
        "openpyxl==3.1.2",
        "Pillow==10.1.0",
        "qrcode==7.4.2",
        "bcrypt==4.1.2"
    ]
    
    print("جاري تثبيت المتطلبات...")
    print()
    
    failed_packages = []
    
    for package in packages:
        print(f"تثبيت {package}...")
        if install_package(package):
            print(f"✓ تم تثبيت {package} بنجاح")
        else:
            print(f"✗ فشل في تثبيت {package}")
            failed_packages.append(package)
        print()
    
    print("=" * 50)
    
    if failed_packages:
        print("فشل في تثبيت الحزم التالية:")
        for package in failed_packages:
            print(f"  - {package}")
        print()
        print("يرجى تثبيتها يدوياً باستخدام:")
        print("pip install <package_name>")
        return False
    else:
        print("✓ تم تثبيت جميع المتطلبات بنجاح!")
        print()
        print("يمكنك الآن تشغيل البرنامج باستخدام:")
        print("python main.py")
        print()
        print("أو استخدام ملف التشغيل السريع:")
        print("run.bat")
        return True

if __name__ == "__main__":
    success = main()
    
    if not success:
        input("\nاضغط Enter للمتابعة...")
        sys.exit(1)
    
    # Ask if user wants to run the program
    print()
    response = input("هل تريد تشغيل البرنامج الآن؟ (y/n): ")
    if response.lower() in ['y', 'yes', 'نعم', 'ن']:
        print()
        print("جاري تشغيل البرنامج...")
        try:
            subprocess.run([sys.executable, "main.py"])
        except KeyboardInterrupt:
            print("\nتم إيقاف البرنامج بواسطة المستخدم")
        except Exception as e:
            print(f"\nخطأ في تشغيل البرنامج: {e}")
    
    input("\nاضغط Enter للخروج...")
