class Translator:
    def __init__(self):
        self.current_language = "ar"
        self.translations = {
            "ar": {
                # Main Menu
                "warehouse_management": "إدارة المستودعات",
                "products": "المنتجات",
                "purchase_invoices": "فواتير الشراء",
                "sales_invoices": "فواتير البيع",
                "reports": "التقارير",
                "settings": "الإعدادات",
                "logout": "تسجيل الخروج",
                
                # Login
                "login": "تسجيل الدخول",
                "username": "اسم المستخدم",
                "password": "كلمة المرور",
                "login_button": "دخول",
                "invalid_credentials": "بيانات الدخول غير صحيحة",
                
                # Products
                "product_code": "كود المنتج",
                "product_name": "اسم المنتج",
                "unit": "وحدة القياس",
                "quantity": "الكمية",
                "status": "الحالة",
                "category": "التصنيف",
                "notes": "الملاحظات",
                "image": "الصورة",
                "add_product": "إضافة منتج",
                "edit_product": "تعديل منتج",
                "delete_product": "حذف منتج",
                "clear_fields": "تفريغ الحقول",
                "search": "بحث",
                "generate_barcode": "توليد باركود",
                "add_unit": "إضافة وحدة قياس",
                "add_status": "إضافة حالة",
                "add_category": "إضافة تصنيف",
                
                # Invoices
                "invoice_number": "رقم الفاتورة",
                "date": "التاريخ",
                "supplier": "المورد",
                "customer": "العميل",
                "total": "الإجمالي",
                "save_invoice": "حفظ الفاتورة",
                "add_item": "إضافة صنف",
                "unit_price": "سعر الوحدة",
                "total_price": "الإجمالي",
                
                # Reports
                "purchase_report": "تقرير المشتريات",
                "sales_report": "تقرير المبيعات",
                "inventory_report": "تقرير المخزون",
                "export_pdf": "تصدير PDF",
                "export_excel": "تصدير Excel",
                "from_date": "من تاريخ",
                "to_date": "إلى تاريخ",
                
                # Settings
                "language": "اللغة",
                "company_name": "اسم الشركة",
                "company_logo": "شعار الشركة",
                "backup": "نسخ احتياطي",
                "restore": "استعادة",
                "save": "حفظ",
                "cancel": "إلغاء",
                
                # Messages
                "success": "تم بنجاح",
                "error": "خطأ",
                "confirm_delete": "هل أنت متأكد من الحذف؟",
                "yes": "نعم",
                "no": "لا",
                "warning": "تحذير",
                "info": "معلومات",
                
                # User Roles
                "admin": "مدير",
                "warehouse_staff": "موظف مخزن",
                "viewer": "مشاهد فقط"
            },
            "en": {
                # Main Menu
                "warehouse_management": "Warehouse Management",
                "products": "Products",
                "purchase_invoices": "Purchase Invoices",
                "sales_invoices": "Sales Invoices",
                "reports": "Reports",
                "settings": "Settings",
                "logout": "Logout",
                
                # Login
                "login": "Login",
                "username": "Username",
                "password": "Password",
                "login_button": "Login",
                "invalid_credentials": "Invalid credentials",
                
                # Products
                "product_code": "Product Code",
                "product_name": "Product Name",
                "unit": "Unit",
                "quantity": "Quantity",
                "status": "Status",
                "category": "Category",
                "notes": "Notes",
                "image": "Image",
                "add_product": "Add Product",
                "edit_product": "Edit Product",
                "delete_product": "Delete Product",
                "clear_fields": "Clear Fields",
                "search": "Search",
                "generate_barcode": "Generate Barcode",
                "add_unit": "Add Unit",
                "add_status": "Add Status",
                "add_category": "Add Category",
                
                # Invoices
                "invoice_number": "Invoice Number",
                "date": "Date",
                "supplier": "Supplier",
                "customer": "Customer",
                "total": "Total",
                "save_invoice": "Save Invoice",
                "add_item": "Add Item",
                "unit_price": "Unit Price",
                "total_price": "Total Price",
                
                # Reports
                "purchase_report": "Purchase Report",
                "sales_report": "Sales Report",
                "inventory_report": "Inventory Report",
                "export_pdf": "Export PDF",
                "export_excel": "Export Excel",
                "from_date": "From Date",
                "to_date": "To Date",
                
                # Settings
                "language": "Language",
                "company_name": "Company Name",
                "company_logo": "Company Logo",
                "backup": "Backup",
                "restore": "Restore",
                "save": "Save",
                "cancel": "Cancel",
                
                # Messages
                "success": "Success",
                "error": "Error",
                "confirm_delete": "Are you sure you want to delete?",
                "yes": "Yes",
                "no": "No",
                "warning": "Warning",
                "info": "Information",
                
                # User Roles
                "admin": "Administrator",
                "warehouse_staff": "Warehouse Staff",
                "viewer": "Viewer Only"
            }
        }
    
    def set_language(self, language):
        """Set current language"""
        if language in self.translations:
            self.current_language = language
    
    def get_text(self, key):
        """Get translated text for current language"""
        return self.translations.get(self.current_language, {}).get(key, key)
    
    def tr(self, key):
        """Shorthand for get_text"""
        return self.get_text(key)

# Global translator instance
translator = Translator()
