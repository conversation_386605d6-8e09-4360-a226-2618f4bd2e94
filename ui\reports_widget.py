from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QComboBox, QTextEdit,
                            QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
                            QDateEdit, QFrame, QTabWidget, QFileDialog)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont
from database.database_manager import DatabaseManager
from utils.translator import translator
from datetime import datetime, timedelta
import os

class ReportsWidget(QWidget):
    def __init__(self, user_data):
        super().__init__()
        self.user_data = user_data
        self.db_manager = DatabaseManager()
        self.init_ui()
        self.apply_styling()
    
    def init_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Create tab widget for different reports
        self.tab_widget = QTabWidget()
        self.tab_widget.setObjectName("reportsTabWidget")
        
        # Purchase reports tab
        purchase_tab = self.create_purchase_reports_tab()
        self.tab_widget.addTab(purchase_tab, translator.tr("purchase_report"))
        
        # Sales reports tab
        sales_tab = self.create_sales_reports_tab()
        self.tab_widget.addTab(sales_tab, translator.tr("sales_report"))
        
        # Inventory report tab
        inventory_tab = self.create_inventory_reports_tab()
        self.tab_widget.addTab(inventory_tab, translator.tr("inventory_report"))
        
        main_layout.addWidget(self.tab_widget)
    
    def create_purchase_reports_tab(self):
        """Create purchase reports tab"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Filters frame
        filters_frame = QFrame()
        filters_frame.setObjectName("filtersFrame")
        filters_layout = QGridLayout(filters_frame)
        filters_layout.setContentsMargins(15, 15, 15, 15)
        
        # Date range
        from_date_label = QLabel(translator.tr("from_date"))
        self.purchase_from_date = QDateEdit()
        self.purchase_from_date.setObjectName("dateField")
        self.purchase_from_date.setDate(QDate.currentDate().addDays(-30))
        self.purchase_from_date.setCalendarPopup(True)
        
        to_date_label = QLabel(translator.tr("to_date"))
        self.purchase_to_date = QDateEdit()
        self.purchase_to_date.setObjectName("dateField")
        self.purchase_to_date.setDate(QDate.currentDate())
        self.purchase_to_date.setCalendarPopup(True)
        
        # Supplier filter
        supplier_label = QLabel(translator.tr("supplier"))
        self.purchase_supplier_input = QLineEdit()
        self.purchase_supplier_input.setObjectName("inputField")
        self.purchase_supplier_input.setPlaceholderText("اسم المورد (اختياري)")
        
        # Buttons
        generate_purchase_btn = QPushButton("إنشاء التقرير")
        generate_purchase_btn.setObjectName("primaryButton")
        generate_purchase_btn.clicked.connect(self.generate_purchase_report)
        
        export_purchase_pdf_btn = QPushButton(translator.tr("export_pdf"))
        export_purchase_pdf_btn.setObjectName("secondaryButton")
        export_purchase_pdf_btn.clicked.connect(lambda: self.export_report("purchase", "pdf"))
        
        export_purchase_excel_btn = QPushButton(translator.tr("export_excel"))
        export_purchase_excel_btn.setObjectName("secondaryButton")
        export_purchase_excel_btn.clicked.connect(lambda: self.export_report("purchase", "excel"))
        
        # Layout filters
        filters_layout.addWidget(from_date_label, 0, 0)
        filters_layout.addWidget(self.purchase_from_date, 0, 1)
        filters_layout.addWidget(to_date_label, 0, 2)
        filters_layout.addWidget(self.purchase_to_date, 0, 3)
        filters_layout.addWidget(supplier_label, 1, 0)
        filters_layout.addWidget(self.purchase_supplier_input, 1, 1, 1, 3)
        
        buttons_layout = QHBoxLayout()
        buttons_layout.addWidget(generate_purchase_btn)
        buttons_layout.addWidget(export_purchase_pdf_btn)
        buttons_layout.addWidget(export_purchase_excel_btn)
        buttons_layout.addStretch()
        
        layout.addWidget(filters_frame)
        layout.addLayout(buttons_layout)
        
        # Results table
        self.purchase_table = QTableWidget()
        self.purchase_table.setObjectName("reportTable")
        self.purchase_table.setColumnCount(6)
        self.purchase_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "التاريخ", "المورد", "عدد الأصناف", "الإجمالي", "المستخدم"
        ])
        
        header = self.purchase_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        
        layout.addWidget(self.purchase_table)
        
        return tab_widget
    
    def create_sales_reports_tab(self):
        """Create sales reports tab"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Filters frame
        filters_frame = QFrame()
        filters_frame.setObjectName("filtersFrame")
        filters_layout = QGridLayout(filters_frame)
        filters_layout.setContentsMargins(15, 15, 15, 15)
        
        # Date range
        from_date_label = QLabel(translator.tr("from_date"))
        self.sales_from_date = QDateEdit()
        self.sales_from_date.setObjectName("dateField")
        self.sales_from_date.setDate(QDate.currentDate().addDays(-30))
        self.sales_from_date.setCalendarPopup(True)
        
        to_date_label = QLabel(translator.tr("to_date"))
        self.sales_to_date = QDateEdit()
        self.sales_to_date.setObjectName("dateField")
        self.sales_to_date.setDate(QDate.currentDate())
        self.sales_to_date.setCalendarPopup(True)
        
        # Customer filter
        customer_label = QLabel(translator.tr("customer"))
        self.sales_customer_input = QLineEdit()
        self.sales_customer_input.setObjectName("inputField")
        self.sales_customer_input.setPlaceholderText("اسم العميل (اختياري)")
        
        # Buttons
        generate_sales_btn = QPushButton("إنشاء التقرير")
        generate_sales_btn.setObjectName("primaryButton")
        generate_sales_btn.clicked.connect(self.generate_sales_report)
        
        export_sales_pdf_btn = QPushButton(translator.tr("export_pdf"))
        export_sales_pdf_btn.setObjectName("secondaryButton")
        export_sales_pdf_btn.clicked.connect(lambda: self.export_report("sales", "pdf"))
        
        export_sales_excel_btn = QPushButton(translator.tr("export_excel"))
        export_sales_excel_btn.setObjectName("secondaryButton")
        export_sales_excel_btn.clicked.connect(lambda: self.export_report("sales", "excel"))
        
        # Layout filters
        filters_layout.addWidget(from_date_label, 0, 0)
        filters_layout.addWidget(self.sales_from_date, 0, 1)
        filters_layout.addWidget(to_date_label, 0, 2)
        filters_layout.addWidget(self.sales_to_date, 0, 3)
        filters_layout.addWidget(customer_label, 1, 0)
        filters_layout.addWidget(self.sales_customer_input, 1, 1, 1, 3)
        
        buttons_layout = QHBoxLayout()
        buttons_layout.addWidget(generate_sales_btn)
        buttons_layout.addWidget(export_sales_pdf_btn)
        buttons_layout.addWidget(export_sales_excel_btn)
        buttons_layout.addStretch()
        
        layout.addWidget(filters_frame)
        layout.addLayout(buttons_layout)
        
        # Results table
        self.sales_table = QTableWidget()
        self.sales_table.setObjectName("reportTable")
        self.sales_table.setColumnCount(6)
        self.sales_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "التاريخ", "العميل", "عدد الأصناف", "الإجمالي", "المستخدم"
        ])
        
        header = self.sales_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        
        layout.addWidget(self.sales_table)
        
        return tab_widget
    
    def create_inventory_reports_tab(self):
        """Create inventory reports tab"""
        tab_widget = QWidget()
        layout = QVBoxLayout(tab_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Filters frame
        filters_frame = QFrame()
        filters_frame.setObjectName("filtersFrame")
        filters_layout = QHBoxLayout(filters_frame)
        filters_layout.setContentsMargins(15, 15, 15, 15)
        
        # Category filter
        category_label = QLabel(translator.tr("category"))
        self.inventory_category_combo = QComboBox()
        self.inventory_category_combo.setObjectName("comboBox")
        self.load_categories()
        
        # Status filter
        status_label = QLabel(translator.tr("status"))
        self.inventory_status_combo = QComboBox()
        self.inventory_status_combo.setObjectName("comboBox")
        self.load_statuses()
        
        # Buttons
        generate_inventory_btn = QPushButton("إنشاء التقرير")
        generate_inventory_btn.setObjectName("primaryButton")
        generate_inventory_btn.clicked.connect(self.generate_inventory_report)
        
        export_inventory_pdf_btn = QPushButton(translator.tr("export_pdf"))
        export_inventory_pdf_btn.setObjectName("secondaryButton")
        export_inventory_pdf_btn.clicked.connect(lambda: self.export_report("inventory", "pdf"))
        
        export_inventory_excel_btn = QPushButton(translator.tr("export_excel"))
        export_inventory_excel_btn.setObjectName("secondaryButton")
        export_inventory_excel_btn.clicked.connect(lambda: self.export_report("inventory", "excel"))
        
        # Layout filters
        filters_layout.addWidget(category_label)
        filters_layout.addWidget(self.inventory_category_combo)
        filters_layout.addWidget(status_label)
        filters_layout.addWidget(self.inventory_status_combo)
        filters_layout.addStretch()
        
        buttons_layout = QHBoxLayout()
        buttons_layout.addWidget(generate_inventory_btn)
        buttons_layout.addWidget(export_inventory_pdf_btn)
        buttons_layout.addWidget(export_inventory_excel_btn)
        buttons_layout.addStretch()
        
        layout.addWidget(filters_frame)
        layout.addLayout(buttons_layout)
        
        # Results table
        self.inventory_table = QTableWidget()
        self.inventory_table.setObjectName("reportTable")
        self.inventory_table.setColumnCount(7)
        self.inventory_table.setHorizontalHeaderLabels([
            "كود المنتج", "اسم المنتج", "الوحدة", "الكمية", "الحالة", "التصنيف", "آخر تحديث"
        ])
        
        header = self.inventory_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        
        layout.addWidget(self.inventory_table)
        
        return tab_widget
    
    def load_categories(self):
        """Load categories for filter"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        language = self.db_manager.get_setting("language") or "ar"
        name_field = "name_ar" if language == "ar" else "name_en"
        
        cursor.execute(f"SELECT id, {name_field} FROM categories ORDER BY {name_field}")
        categories = cursor.fetchall()
        
        self.inventory_category_combo.clear()
        self.inventory_category_combo.addItem("جميع التصنيفات", None)
        
        for category_id, name in categories:
            self.inventory_category_combo.addItem(name, category_id)
        
        conn.close()
    
    def load_statuses(self):
        """Load statuses for filter"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        language = self.db_manager.get_setting("language") or "ar"
        name_field = "name_ar" if language == "ar" else "name_en"
        
        cursor.execute(f"SELECT id, {name_field} FROM item_status ORDER BY {name_field}")
        statuses = cursor.fetchall()
        
        self.inventory_status_combo.clear()
        self.inventory_status_combo.addItem("جميع الحالات", None)
        
        for status_id, name in statuses:
            self.inventory_status_combo.addItem(name, status_id)
        
        conn.close()
    
    def generate_purchase_report(self):
        """Generate purchase report"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        from_date = self.purchase_from_date.date().toString("yyyy-MM-dd")
        to_date = self.purchase_to_date.date().toString("yyyy-MM-dd")
        supplier_filter = self.purchase_supplier_input.text().strip()
        
        query = """
            SELECT pi.invoice_number, pi.invoice_date, pi.supplier_name,
                   COUNT(pii.id) as item_count, pi.total_amount, u.username
            FROM purchase_invoices pi
            LEFT JOIN purchase_invoice_items pii ON pi.id = pii.invoice_id
            LEFT JOIN users u ON pi.created_by = u.id
            WHERE pi.invoice_date BETWEEN ? AND ?
        """
        params = [from_date, to_date]
        
        if supplier_filter:
            query += " AND pi.supplier_name LIKE ?"
            params.append(f"%{supplier_filter}%")
        
        query += " GROUP BY pi.id ORDER BY pi.invoice_date DESC"
        
        cursor.execute(query, params)
        results = cursor.fetchall()
        
        # Populate table
        self.purchase_table.setRowCount(len(results))
        total_amount = 0
        
        for row, data in enumerate(results):
            for col, value in enumerate(data):
                if col == 4:  # Total amount column
                    total_amount += float(value) if value else 0
                self.purchase_table.setItem(row, col, QTableWidgetItem(str(value) if value else ""))
        
        # Show summary
        QMessageBox.information(self, "ملخص التقرير", 
                              f"عدد الفواتير: {len(results)}\nإجمالي المبلغ: {total_amount:.2f}")
        
        conn.close()
    
    def generate_sales_report(self):
        """Generate sales report"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        from_date = self.sales_from_date.date().toString("yyyy-MM-dd")
        to_date = self.sales_to_date.date().toString("yyyy-MM-dd")
        customer_filter = self.sales_customer_input.text().strip()
        
        query = """
            SELECT si.invoice_number, si.invoice_date, si.customer_name,
                   COUNT(sii.id) as item_count, si.total_amount, u.username
            FROM sales_invoices si
            LEFT JOIN sales_invoice_items sii ON si.id = sii.invoice_id
            LEFT JOIN users u ON si.created_by = u.id
            WHERE si.invoice_date BETWEEN ? AND ?
        """
        params = [from_date, to_date]
        
        if customer_filter:
            query += " AND si.customer_name LIKE ?"
            params.append(f"%{customer_filter}%")
        
        query += " GROUP BY si.id ORDER BY si.invoice_date DESC"
        
        cursor.execute(query, params)
        results = cursor.fetchall()
        
        # Populate table
        self.sales_table.setRowCount(len(results))
        total_amount = 0
        
        for row, data in enumerate(results):
            for col, value in enumerate(data):
                if col == 4:  # Total amount column
                    total_amount += float(value) if value else 0
                self.sales_table.setItem(row, col, QTableWidgetItem(str(value) if value else ""))
        
        # Show summary
        QMessageBox.information(self, "ملخص التقرير", 
                              f"عدد الفواتير: {len(results)}\nإجمالي المبلغ: {total_amount:.2f}")
        
        conn.close()
    
    def generate_inventory_report(self):
        """Generate inventory report"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        language = self.db_manager.get_setting("language") or "ar"
        name_field = "name_ar" if language == "ar" else "name_en"
        
        query = f"""
            SELECT p.code, p.{name_field}, u.{name_field}, p.quantity,
                   s.{name_field}, c.{name_field}, p.updated_at
            FROM products p
            LEFT JOIN units u ON p.unit_id = u.id
            LEFT JOIN item_status s ON p.status_id = s.id
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE 1=1
        """
        params = []
        
        # Apply filters
        category_id = self.inventory_category_combo.currentData()
        if category_id:
            query += " AND p.category_id = ?"
            params.append(category_id)
        
        status_id = self.inventory_status_combo.currentData()
        if status_id:
            query += " AND p.status_id = ?"
            params.append(status_id)
        
        query += " ORDER BY p.code"
        
        cursor.execute(query, params)
        results = cursor.fetchall()
        
        # Populate table
        self.inventory_table.setRowCount(len(results))
        total_items = len(results)
        total_quantity = 0
        
        for row, data in enumerate(results):
            for col, value in enumerate(data):
                if col == 3:  # Quantity column
                    total_quantity += float(value) if value else 0
                self.inventory_table.setItem(row, col, QTableWidgetItem(str(value) if value else ""))
        
        # Show summary
        QMessageBox.information(self, "ملخص التقرير", 
                              f"عدد المنتجات: {total_items}\nإجمالي الكمية: {total_quantity:.2f}")
        
        conn.close()
    
    def export_report(self, report_type, format_type):
        """Export report to PDF or Excel"""
        try:
            if format_type == "pdf":
                self.export_to_pdf(report_type)
            elif format_type == "excel":
                self.export_to_excel(report_type)
        except Exception as e:
            QMessageBox.critical(self, translator.tr("error"), f"خطأ في التصدير: {str(e)}")
    
    def export_to_pdf(self, report_type):
        """Export report to PDF"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, f"حفظ تقرير {report_type}", 
            f"report_{report_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
            "PDF Files (*.pdf)"
        )
        
        if file_path:
            # Implementation for PDF export using reportlab
            QMessageBox.information(self, translator.tr("success"), f"تم تصدير التقرير إلى: {file_path}")
    
    def export_to_excel(self, report_type):
        """Export report to Excel"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, f"حفظ تقرير {report_type}", 
            f"report_{report_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            "Excel Files (*.xlsx)"
        )
        
        if file_path:
            # Implementation for Excel export using openpyxl
            QMessageBox.information(self, translator.tr("success"), f"تم تصدير التقرير إلى: {file_path}")
    
    def apply_styling(self):
        """Apply widget styling"""
        self.setStyleSheet("""
            #reportsTabWidget::pane {
                border: 1px solid #555555;
                background-color: #3c3c3c;
            }
            
            #filtersFrame {
                background-color: #404040;
                border-radius: 8px;
                border: 1px solid #555555;
                margin-bottom: 10px;
            }
            
            #inputField, #dateField {
                padding: 8px;
                border: 1px solid #555555;
                border-radius: 4px;
                background-color: #454545;
                color: #ffffff;
            }
            
            #comboBox {
                padding: 8px;
                border: 1px solid #555555;
                border-radius: 4px;
                background-color: #454545;
                color: #ffffff;
            }
            
            #primaryButton {
                padding: 10px 20px;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            
            #primaryButton:hover {
                background-color: #45a049;
            }
            
            #secondaryButton {
                padding: 10px 20px;
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
            }
            
            #secondaryButton:hover {
                background-color: #1976D2;
            }
            
            #reportTable {
                background-color: #404040;
                alternate-background-color: #454545;
                selection-background-color: #4CAF50;
                gridline-color: #555555;
                color: #ffffff;
            }
            
            QHeaderView::section {
                background-color: #505050;
                color: #ffffff;
                padding: 8px;
                border: 1px solid #555555;
                font-weight: bold;
            }
        """)
