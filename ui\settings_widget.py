from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QComboBox, QTextEdit,
                            QGroupBox, QFileDialog, QMessageBox, QFrame)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPixmap
from database.database_manager import DatabaseManager
from utils.translator import translator
import os
import shutil
from datetime import datetime

class SettingsWidget(QWidget):
    def __init__(self, user_data):
        super().__init__()
        self.user_data = user_data
        self.db_manager = DatabaseManager()
        self.init_ui()
        self.load_settings()
        self.apply_styling()
    
    def init_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Language settings
        language_group = self.create_language_group()
        main_layout.addWidget(language_group)
        
        # Company settings
        company_group = self.create_company_group()
        main_layout.addWidget(company_group)
        
        # Backup settings
        backup_group = self.create_backup_group()
        main_layout.addWidget(backup_group)
        
        # User management (admin only)
        if self.user_data.get("role") == "admin":
            user_group = self.create_user_group()
            main_layout.addWidget(user_group)
        
        # Save button
        save_layout = QHBoxLayout()
        save_layout.addStretch()
        
        save_button = QPushButton(translator.tr("save"))
        save_button.setObjectName("primaryButton")
        save_button.clicked.connect(self.save_settings)
        
        save_layout.addWidget(save_button)
        main_layout.addLayout(save_layout)
        
        main_layout.addStretch()
    
    def create_language_group(self):
        """Create language settings group"""
        group = QGroupBox(translator.tr("language"))
        group.setObjectName("settingsGroup")
        layout = QGridLayout(group)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Language selection
        language_label = QLabel("اختيار اللغة:")
        self.language_combo = QComboBox()
        self.language_combo.setObjectName("comboBox")
        self.language_combo.addItem("العربية", "ar")
        self.language_combo.addItem("English", "en")
        
        layout.addWidget(language_label, 0, 0)
        layout.addWidget(self.language_combo, 0, 1)
        
        return group
    
    def create_company_group(self):
        """Create company settings group"""
        group = QGroupBox(translator.tr("company_name"))
        group.setObjectName("settingsGroup")
        layout = QGridLayout(group)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Company name
        name_label = QLabel("اسم الشركة:")
        self.company_name_input = QLineEdit()
        self.company_name_input.setObjectName("inputField")
        
        layout.addWidget(name_label, 0, 0)
        layout.addWidget(self.company_name_input, 0, 1, 1, 2)
        
        # Company logo
        logo_label = QLabel("شعار الشركة:")
        self.logo_button = QPushButton("اختيار شعار")
        self.logo_button.setObjectName("secondaryButton")
        self.logo_button.clicked.connect(self.select_logo)
        
        self.logo_preview = QLabel()
        self.logo_preview.setObjectName("logoPreview")
        self.logo_preview.setFixedSize(100, 100)
        self.logo_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.logo_preview.setStyleSheet("border: 1px solid #555555; background-color: #404040;")
        
        layout.addWidget(logo_label, 1, 0)
        layout.addWidget(self.logo_button, 1, 1)
        layout.addWidget(self.logo_preview, 1, 2)
        
        return group
    
    def create_backup_group(self):
        """Create backup settings group"""
        group = QGroupBox("النسخ الاحتياطي")
        group.setObjectName("settingsGroup")
        layout = QGridLayout(group)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Manual backup
        backup_label = QLabel("إنشاء نسخة احتياطية:")
        backup_button = QPushButton("إنشاء نسخة احتياطية")
        backup_button.setObjectName("primaryButton")
        backup_button.clicked.connect(self.create_backup)
        
        layout.addWidget(backup_label, 0, 0)
        layout.addWidget(backup_button, 0, 1)
        
        # Restore backup
        restore_label = QLabel("استعادة نسخة احتياطية:")
        restore_button = QPushButton("استعادة من ملف")
        restore_button.setObjectName("secondaryButton")
        restore_button.clicked.connect(self.restore_backup)
        
        layout.addWidget(restore_label, 1, 0)
        layout.addWidget(restore_button, 1, 1)
        
        # Auto backup settings
        auto_backup_label = QLabel("النسخ الاحتياطي التلقائي:")
        self.auto_backup_combo = QComboBox()
        self.auto_backup_combo.setObjectName("comboBox")
        self.auto_backup_combo.addItem("معطل", "0")
        self.auto_backup_combo.addItem("يومي", "1")
        self.auto_backup_combo.addItem("أسبوعي", "7")
        self.auto_backup_combo.addItem("شهري", "30")
        
        layout.addWidget(auto_backup_label, 2, 0)
        layout.addWidget(self.auto_backup_combo, 2, 1)
        
        return group
    
    def create_user_group(self):
        """Create user management group (admin only)"""
        group = QGroupBox("إدارة المستخدمين")
        group.setObjectName("settingsGroup")
        layout = QVBoxLayout(group)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Add user button
        add_user_button = QPushButton("إضافة مستخدم جديد")
        add_user_button.setObjectName("primaryButton")
        add_user_button.clicked.connect(self.add_new_user)
        
        # Manage users button
        manage_users_button = QPushButton("إدارة المستخدمين")
        manage_users_button.setObjectName("secondaryButton")
        manage_users_button.clicked.connect(self.manage_users)
        
        buttons_layout = QHBoxLayout()
        buttons_layout.addWidget(add_user_button)
        buttons_layout.addWidget(manage_users_button)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        return group
    
    def load_settings(self):
        """Load current settings"""
        # Load language
        current_language = self.db_manager.get_setting("language") or "ar"
        for i in range(self.language_combo.count()):
            if self.language_combo.itemData(i) == current_language:
                self.language_combo.setCurrentIndex(i)
                break
        
        # Load company name
        company_name = self.db_manager.get_setting("company_name") or ""
        self.company_name_input.setText(company_name)
        
        # Load company logo
        logo_path = self.db_manager.get_setting("company_logo")
        if logo_path and os.path.exists(logo_path):
            pixmap = QPixmap(logo_path)
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(100, 100, Qt.AspectRatioMode.KeepAspectRatio, 
                                            Qt.TransformationMode.SmoothTransformation)
                self.logo_preview.setPixmap(scaled_pixmap)
        
        # Load auto backup setting
        auto_backup = self.db_manager.get_setting("auto_backup") or "0"
        for i in range(self.auto_backup_combo.count()):
            if self.auto_backup_combo.itemData(i) == auto_backup:
                self.auto_backup_combo.setCurrentIndex(i)
                break
    
    def select_logo(self):
        """Select company logo"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار شعار الشركة", "", 
            "Image Files (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        
        if file_path:
            # Copy logo to resources directory
            if not os.path.exists("resources"):
                os.makedirs("resources")
            
            logo_filename = f"company_logo{os.path.splitext(file_path)[1]}"
            logo_destination = os.path.join("resources", logo_filename)
            
            try:
                shutil.copy2(file_path, logo_destination)
                
                # Update preview
                pixmap = QPixmap(logo_destination)
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(100, 100, Qt.AspectRatioMode.KeepAspectRatio, 
                                                Qt.TransformationMode.SmoothTransformation)
                    self.logo_preview.setPixmap(scaled_pixmap)
                
                # Store path for saving
                self.logo_path = logo_destination
                
            except Exception as e:
                QMessageBox.critical(self, translator.tr("error"), f"خطأ في نسخ الشعار: {str(e)}")
    
    def save_settings(self):
        """Save all settings"""
        try:
            # Save language
            selected_language = self.language_combo.currentData()
            self.db_manager.set_setting("language", selected_language)
            
            # Save company name
            company_name = self.company_name_input.text().strip()
            self.db_manager.set_setting("company_name", company_name)
            
            # Save company logo if changed
            if hasattr(self, 'logo_path'):
                self.db_manager.set_setting("company_logo", self.logo_path)
            
            # Save auto backup setting
            auto_backup = self.auto_backup_combo.currentData()
            self.db_manager.set_setting("auto_backup", auto_backup)
            
            # Update translator language
            translator.set_language(selected_language)
            
            QMessageBox.information(self, translator.tr("success"), 
                                  "تم حفظ الإعدادات بنجاح\nيرجى إعادة تشغيل البرنامج لتطبيق التغييرات")
            
        except Exception as e:
            QMessageBox.critical(self, translator.tr("error"), f"خطأ في حفظ الإعدادات: {str(e)}")
    
    def create_backup(self):
        """Create database backup"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ النسخة الاحتياطية", 
            f"warehouse_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db",
            "Database Files (*.db)"
        )
        
        if file_path:
            if self.db_manager.backup_database(file_path):
                QMessageBox.information(self, translator.tr("success"), 
                                      f"تم إنشاء النسخة الاحتياطية بنجاح\n{file_path}")
            else:
                QMessageBox.critical(self, translator.tr("error"), "فشل في إنشاء النسخة الاحتياطية")
    
    def restore_backup(self):
        """Restore database from backup"""
        reply = QMessageBox.question(
            self, "تأكيد الاستعادة",
            "تحذير: سيتم استبدال قاعدة البيانات الحالية بالكامل\nهل أنت متأكد من المتابعة؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "اختيار ملف النسخة الاحتياطية", "",
                "Database Files (*.db)"
            )
            
            if file_path:
                if self.db_manager.restore_database(file_path):
                    QMessageBox.information(self, translator.tr("success"), 
                                          "تم استعادة النسخة الاحتياطية بنجاح\nيرجى إعادة تشغيل البرنامج")
                else:
                    QMessageBox.critical(self, translator.tr("error"), "فشل في استعادة النسخة الاحتياطية")
    
    def add_new_user(self):
        """Add new user dialog"""
        from ui.dialogs.add_user_dialog import AddUserDialog
        dialog = AddUserDialog(self)
        dialog.exec()
    
    def manage_users(self):
        """Manage users dialog"""
        from ui.dialogs.manage_users_dialog import ManageUsersDialog
        dialog = ManageUsersDialog(self)
        dialog.exec()
    
    def apply_styling(self):
        """Apply widget styling"""
        self.setStyleSheet("""
            #settingsGroup {
                font-size: 14px;
                font-weight: bold;
                color: #4CAF50;
                border: 2px solid #555555;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            
            #settingsGroup::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: #3c3c3c;
            }
            
            #inputField {
                padding: 8px;
                border: 1px solid #555555;
                border-radius: 4px;
                background-color: #404040;
                color: #ffffff;
                font-size: 12px;
            }
            
            #inputField:focus {
                border-color: #4CAF50;
            }
            
            #comboBox {
                padding: 8px;
                border: 1px solid #555555;
                border-radius: 4px;
                background-color: #404040;
                color: #ffffff;
                font-size: 12px;
            }
            
            #primaryButton {
                padding: 10px 20px;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
            }
            
            #primaryButton:hover {
                background-color: #45a049;
            }
            
            #secondaryButton {
                padding: 10px 20px;
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
            }
            
            #secondaryButton:hover {
                background-color: #1976D2;
            }
            
            QLabel {
                color: #ffffff;
                font-size: 12px;
            }
        """)
