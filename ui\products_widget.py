from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QComboBox, QTextEdit,
                            QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
                            QFileDialog, QFrame, QSpacerItem, QSizePolicy)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPixmap
from database.database_manager import DatabaseManager
from utils.translator import translator
from utils.barcode_generator import BarcodeGenerator
import os

class ProductsWidget(QWidget):
    def __init__(self, user_data):
        super().__init__()
        self.user_data = user_data
        self.db_manager = DatabaseManager()
        self.current_product_id = None
        self.init_ui()
        self.load_data()
        self.apply_styling()
    
    def init_ui(self):
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Left panel - Product form
        left_panel = self.create_product_form()
        main_layout.addWidget(left_panel, 1)
        
        # Right panel - Products table
        right_panel = self.create_products_table()
        main_layout.addWidget(right_panel, 2)
    
    def create_product_form(self):
        """Create product input form"""
        form_frame = QFrame()
        form_frame.setObjectName("formFrame")
        form_layout = QVBoxLayout(form_frame)
        form_layout.setContentsMargins(20, 20, 20, 20)
        form_layout.setSpacing(15)
        
        # Title
        title_label = QLabel(translator.tr("add_product"))
        title_label.setObjectName("formTitle")
        form_layout.addWidget(title_label)
        
        # Product code with generate button
        code_layout = QHBoxLayout()
        code_label = QLabel(translator.tr("product_code"))
        self.code_input = QLineEdit()
        self.code_input.setObjectName("inputField")
        generate_code_btn = QPushButton(translator.tr("generate_barcode"))
        generate_code_btn.setObjectName("secondaryButton")
        generate_code_btn.clicked.connect(self.generate_product_code)
        
        code_layout.addWidget(self.code_input, 2)
        code_layout.addWidget(generate_code_btn, 1)
        
        form_layout.addWidget(code_label)
        form_layout.addLayout(code_layout)
        
        # Product name
        name_label = QLabel(translator.tr("product_name"))
        self.name_input = QLineEdit()
        self.name_input.setObjectName("inputField")
        form_layout.addWidget(name_label)
        form_layout.addWidget(self.name_input)
        
        # Unit with add button
        unit_layout = QHBoxLayout()
        unit_label = QLabel(translator.tr("unit"))
        self.unit_combo = QComboBox()
        self.unit_combo.setObjectName("comboBox")
        add_unit_btn = QPushButton(translator.tr("add_unit"))
        add_unit_btn.setObjectName("secondaryButton")
        add_unit_btn.clicked.connect(self.add_new_unit)
        
        unit_layout.addWidget(self.unit_combo, 2)
        unit_layout.addWidget(add_unit_btn, 1)
        
        form_layout.addWidget(unit_label)
        form_layout.addLayout(unit_layout)
        
        # Quantity
        quantity_label = QLabel(translator.tr("quantity"))
        self.quantity_input = QLineEdit()
        self.quantity_input.setObjectName("inputField")
        self.quantity_input.setText("0")
        form_layout.addWidget(quantity_label)
        form_layout.addWidget(self.quantity_input)
        
        # Status with add button
        status_layout = QHBoxLayout()
        status_label = QLabel(translator.tr("status"))
        self.status_combo = QComboBox()
        self.status_combo.setObjectName("comboBox")
        add_status_btn = QPushButton(translator.tr("add_status"))
        add_status_btn.setObjectName("secondaryButton")
        add_status_btn.clicked.connect(self.add_new_status)
        
        status_layout.addWidget(self.status_combo, 2)
        status_layout.addWidget(add_status_btn, 1)
        
        form_layout.addWidget(status_label)
        form_layout.addLayout(status_layout)
        
        # Category with add button
        category_layout = QHBoxLayout()
        category_label = QLabel(translator.tr("category"))
        self.category_combo = QComboBox()
        self.category_combo.setObjectName("comboBox")
        add_category_btn = QPushButton(translator.tr("add_category"))
        add_category_btn.setObjectName("secondaryButton")
        add_category_btn.clicked.connect(self.add_new_category)
        
        category_layout.addWidget(self.category_combo, 2)
        category_layout.addWidget(add_category_btn, 1)
        
        form_layout.addWidget(category_label)
        form_layout.addLayout(category_layout)
        
        # Notes
        notes_label = QLabel(translator.tr("notes"))
        self.notes_input = QTextEdit()
        self.notes_input.setObjectName("textArea")
        self.notes_input.setMaximumHeight(80)
        form_layout.addWidget(notes_label)
        form_layout.addWidget(self.notes_input)
        
        # Image
        image_label = QLabel(translator.tr("image"))
        self.image_button = QPushButton("اختيار صورة")
        self.image_button.setObjectName("secondaryButton")
        self.image_button.clicked.connect(self.select_image)
        self.image_path = ""
        form_layout.addWidget(image_label)
        form_layout.addWidget(self.image_button)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        self.add_button = QPushButton(translator.tr("add_product"))
        self.add_button.setObjectName("primaryButton")
        self.add_button.clicked.connect(self.add_product)
        
        self.edit_button = QPushButton(translator.tr("edit_product"))
        self.edit_button.setObjectName("primaryButton")
        self.edit_button.clicked.connect(self.edit_product)
        self.edit_button.setEnabled(False)
        
        self.delete_button = QPushButton(translator.tr("delete_product"))
        self.delete_button.setObjectName("dangerButton")
        self.delete_button.clicked.connect(self.delete_product)
        self.delete_button.setEnabled(False)
        
        self.clear_button = QPushButton(translator.tr("clear_fields"))
        self.clear_button.setObjectName("secondaryButton")
        self.clear_button.clicked.connect(self.clear_fields)
        
        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.edit_button)
        buttons_layout.addWidget(self.delete_button)
        buttons_layout.addWidget(self.clear_button)
        
        form_layout.addLayout(buttons_layout)
        form_layout.addStretch()
        
        return form_frame
    
    def create_products_table(self):
        """Create products table"""
        table_frame = QFrame()
        table_frame.setObjectName("tableFrame")
        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(20, 20, 20, 20)
        
        # Search
        search_layout = QHBoxLayout()
        search_label = QLabel(translator.tr("search"))
        self.search_input = QLineEdit()
        self.search_input.setObjectName("inputField")
        self.search_input.setPlaceholderText("البحث في المنتجات...")
        self.search_input.textChanged.connect(self.search_products)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        table_layout.addLayout(search_layout)
        
        # Table
        self.products_table = QTableWidget()
        self.products_table.setObjectName("dataTable")
        self.products_table.setColumnCount(7)
        self.products_table.setHorizontalHeaderLabels([
            translator.tr("product_code"),
            translator.tr("product_name"),
            translator.tr("unit"),
            translator.tr("quantity"),
            translator.tr("status"),
            translator.tr("category"),
            translator.tr("notes")
        ])
        
        # Configure table
        header = self.products_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        
        self.products_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.products_table.setAlternatingRowColors(True)
        self.products_table.itemSelectionChanged.connect(self.on_product_selected)
        
        table_layout.addWidget(self.products_table)
        
        return table_frame
    
    def load_data(self):
        """Load data into combo boxes and table"""
        self.load_units()
        self.load_statuses()
        self.load_categories()
        self.load_products()
    
    def load_units(self):
        """Load units into combo box"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        language = self.db_manager.get_setting("language") or "ar"
        name_field = "name_ar" if language == "ar" else "name_en"
        
        cursor.execute(f"SELECT id, {name_field} FROM units ORDER BY {name_field}")
        units = cursor.fetchall()
        
        self.unit_combo.clear()
        for unit_id, name in units:
            self.unit_combo.addItem(name, unit_id)
        
        conn.close()
    
    def load_statuses(self):
        """Load statuses into combo box"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        language = self.db_manager.get_setting("language") or "ar"
        name_field = "name_ar" if language == "ar" else "name_en"
        
        cursor.execute(f"SELECT id, {name_field} FROM item_status ORDER BY {name_field}")
        statuses = cursor.fetchall()
        
        self.status_combo.clear()
        for status_id, name in statuses:
            self.status_combo.addItem(name, status_id)
        
        conn.close()
    
    def load_categories(self):
        """Load categories into combo box"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        language = self.db_manager.get_setting("language") or "ar"
        name_field = "name_ar" if language == "ar" else "name_en"
        
        cursor.execute(f"SELECT id, {name_field} FROM categories ORDER BY {name_field}")
        categories = cursor.fetchall()
        
        self.category_combo.clear()
        for category_id, name in categories:
            self.category_combo.addItem(name, category_id)
        
        conn.close()
    
    def load_products(self):
        """Load products into table"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        language = self.db_manager.get_setting("language") or "ar"
        name_field = "name_ar" if language == "ar" else "name_en"
        
        cursor.execute(f"""
            SELECT p.id, p.code, p.{name_field.replace('name_', 'name_')}, 
                   u.{name_field}, p.quantity, s.{name_field}, c.{name_field}, p.notes
            FROM products p
            LEFT JOIN units u ON p.unit_id = u.id
            LEFT JOIN item_status s ON p.status_id = s.id
            LEFT JOIN categories c ON p.category_id = c.id
            ORDER BY p.created_at DESC
        """)
        
        products = cursor.fetchall()
        
        self.products_table.setRowCount(len(products))
        
        for row, product in enumerate(products):
            for col, value in enumerate(product[1:]):  # Skip ID
                item = QTableWidgetItem(str(value) if value else "")
                self.products_table.setItem(row, col, item)
        
        conn.close()
    
    def generate_product_code(self):
        """Generate random product code"""
        code = BarcodeGenerator.generate_random_code()
        self.code_input.setText(code)
    
    def add_new_unit(self):
        """Add new unit dialog"""
        from ui.dialogs.add_unit_dialog import AddUnitDialog
        dialog = AddUnitDialog(self)
        if dialog.exec():
            self.load_units()
    
    def add_new_status(self):
        """Add new status dialog"""
        from ui.dialogs.add_status_dialog import AddStatusDialog
        dialog = AddStatusDialog(self)
        if dialog.exec():
            self.load_statuses()
    
    def add_new_category(self):
        """Add new category dialog"""
        from ui.dialogs.add_category_dialog import AddCategoryDialog
        dialog = AddCategoryDialog(self)
        if dialog.exec():
            self.load_categories()
    
    def select_image(self):
        """Select product image"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار صورة المنتج", "", 
            "Image Files (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        if file_path:
            self.image_path = file_path
            self.image_button.setText(f"تم اختيار: {os.path.basename(file_path)}")
    
    def add_product(self):
        """Add new product"""
        if not self.validate_input():
            return
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute("""
                INSERT INTO products (code, name_ar, name_en, unit_id, quantity, 
                                    status_id, category_id, notes, image_path)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                self.code_input.text(),
                self.name_input.text(),
                self.name_input.text(),  # For now, same name for both languages
                self.unit_combo.currentData(),
                float(self.quantity_input.text()),
                self.status_combo.currentData(),
                self.category_combo.currentData(),
                self.notes_input.toPlainText(),
                self.image_path
            ))
            
            conn.commit()
            QMessageBox.information(self, translator.tr("success"), "تم إضافة المنتج بنجاح")
            self.clear_fields()
            self.load_products()
            
        except Exception as e:
            QMessageBox.critical(self, translator.tr("error"), f"خطأ في إضافة المنتج: {str(e)}")
        finally:
            conn.close()
    
    def validate_input(self):
        """Validate input fields"""
        if not self.code_input.text().strip():
            QMessageBox.warning(self, translator.tr("warning"), "يرجى إدخال كود المنتج")
            return False
        
        if not self.name_input.text().strip():
            QMessageBox.warning(self, translator.tr("warning"), "يرجى إدخال اسم المنتج")
            return False
        
        try:
            float(self.quantity_input.text())
        except ValueError:
            QMessageBox.warning(self, translator.tr("warning"), "يرجى إدخال كمية صحيحة")
            return False
        
        return True
    
    def clear_fields(self):
        """Clear all input fields"""
        self.code_input.clear()
        self.name_input.clear()
        self.quantity_input.setText("0")
        self.notes_input.clear()
        self.image_path = ""
        self.image_button.setText("اختيار صورة")
        self.current_product_id = None
        
        self.add_button.setEnabled(True)
        self.edit_button.setEnabled(False)
        self.delete_button.setEnabled(False)
    
    def on_product_selected(self):
        """Handle product selection"""
        current_row = self.products_table.currentRow()
        if current_row >= 0:
            # Get product data and populate form
            # This would require getting the product ID and loading full data
            self.add_button.setEnabled(False)
            self.edit_button.setEnabled(True)
            self.delete_button.setEnabled(True)
    
    def search_products(self):
        """Search products"""
        search_text = self.search_input.text().lower()
        
        for row in range(self.products_table.rowCount()):
            match = False
            for col in range(self.products_table.columnCount()):
                item = self.products_table.item(row, col)
                if item and search_text in item.text().lower():
                    match = True
                    break
            
            self.products_table.setRowHidden(row, not match)
    
    def edit_product(self):
        """Edit selected product"""
        # Implementation for editing product
        pass
    
    def delete_product(self):
        """Delete selected product"""
        # Implementation for deleting product
        pass
    
    def apply_styling(self):
        """Apply widget styling"""
        self.setStyleSheet("""
            #formFrame {
                background-color: #3c3c3c;
                border-radius: 8px;
                border: 1px solid #555555;
            }
            
            #tableFrame {
                background-color: #3c3c3c;
                border-radius: 8px;
                border: 1px solid #555555;
            }
            
            #formTitle {
                font-size: 16px;
                font-weight: bold;
                color: #4CAF50;
                margin-bottom: 10px;
            }
            
            #inputField {
                padding: 8px;
                border: 1px solid #555555;
                border-radius: 4px;
                background-color: #404040;
                color: #ffffff;
            }
            
            #inputField:focus {
                border-color: #4CAF50;
            }
            
            #comboBox {
                padding: 8px;
                border: 1px solid #555555;
                border-radius: 4px;
                background-color: #404040;
                color: #ffffff;
            }
            
            #textArea {
                border: 1px solid #555555;
                border-radius: 4px;
                background-color: #404040;
                color: #ffffff;
            }
            
            #primaryButton {
                padding: 10px 15px;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            
            #primaryButton:hover {
                background-color: #45a049;
            }
            
            #secondaryButton {
                padding: 8px 12px;
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
            }
            
            #secondaryButton:hover {
                background-color: #1976D2;
            }
            
            #dangerButton {
                padding: 10px 15px;
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            
            #dangerButton:hover {
                background-color: #da190b;
            }
            
            #dataTable {
                background-color: #404040;
                alternate-background-color: #454545;
                selection-background-color: #4CAF50;
                gridline-color: #555555;
                color: #ffffff;
            }
            
            #dataTable::item {
                padding: 8px;
            }
            
            QHeaderView::section {
                background-color: #505050;
                color: #ffffff;
                padding: 8px;
                border: 1px solid #555555;
                font-weight: bold;
            }
        """)
