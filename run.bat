@echo off
echo ========================================
echo    نظام إدارة فواتير المستودعات
echo ========================================
echo.
echo جاري تشغيل البرنامج...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من python.org
    pause
    exit /b 1
)

REM Check if required packages are installed
echo جاري التحقق من المتطلبات...
pip show PyQt6 >nul 2>&1
if errorlevel 1 (
    echo جاري تثبيت المتطلبات...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo خطأ في تثبيت المتطلبات
        pause
        exit /b 1
    )
)

REM Run the application
echo.
echo بدء تشغيل البرنامج...
echo.
echo بيانات الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.

python main.py

if errorlevel 1 (
    echo.
    echo خطأ في تشغيل البرنامج
    pause
)

echo.
echo تم إغلاق البرنامج
pause
