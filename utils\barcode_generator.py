import qrcode
import random
import string
from datetime import datetime

class BarcodeGenerator:
    @staticmethod
    def generate_random_code(length=8):
        """Generate random alphanumeric code"""
        characters = string.ascii_uppercase + string.digits
        return ''.join(random.choice(characters) for _ in range(length))
    
    @staticmethod
    def generate_sequential_code(prefix="PRD", last_number=0):
        """Generate sequential code with prefix"""
        return f"{prefix}{last_number + 1:06d}"
    
    @staticmethod
    def generate_qr_code(data, file_path):
        """Generate QR code and save to file"""
        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(data)
            qr.make(fit=True)
            
            img = qr.make_image(fill_color="black", back_color="white")
            img.save(file_path)
            return True
        except Exception as e:
            print(f"Error generating QR code: {e}")
            return False
    
    @staticmethod
    def generate_timestamp_code():
        """Generate code based on timestamp"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        return f"TS{timestamp}"
