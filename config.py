# -*- coding: utf-8 -*-

"""
Configuration file for Warehouse Management System
ملف إعدادات نظام إدارة المستودعات
"""

import os

# Application Information
APP_NAME = "نظام إدارة المستودعات"
APP_VERSION = "1.0.0"
APP_AUTHOR = "Warehouse Management System"

# Database Configuration
DATABASE_NAME = "warehouse.db"
DATABASE_PATH = os.path.join(os.getcwd(), DATABASE_NAME)

# Backup Configuration
BACKUP_FOLDER = "backups"
BACKUP_PATH = os.path.join(os.getcwd(), BACKUP_FOLDER)

# Resources Configuration
RESOURCES_FOLDER = "resources"
RESOURCES_PATH = os.path.join(os.getcwd(), RESOURCES_FOLDER)

# UI Configuration
DEFAULT_LANGUAGE = "ar"
SUPPORTED_LANGUAGES = ["ar", "en"]

# Window Configuration
MAIN_WINDOW_SIZE = (1200, 800)
LOGIN_WINDOW_SIZE = (400, 300)

# Theme Configuration
DARK_THEME = {
    "primary_color": "#4CAF50",
    "secondary_color": "#2196F3", 
    "danger_color": "#f44336",
    "warning_color": "#FFC107",
    "background_color": "#2b2b2b",
    "surface_color": "#3c3c3c",
    "input_color": "#404040",
    "text_color": "#ffffff",
    "border_color": "#555555"
}

# Default User Configuration
DEFAULT_ADMIN = {
    "username": "admin",
    "password": "admin123",
    "role": "admin",
    "full_name": "مدير النظام"
}

# File Extensions
ALLOWED_IMAGE_EXTENSIONS = [".png", ".jpg", ".jpeg", ".bmp", ".gif"]
EXPORT_FORMATS = ["pdf", "xlsx"]

# Validation Rules
MIN_PASSWORD_LENGTH = 6
MAX_USERNAME_LENGTH = 50
MAX_PRODUCT_CODE_LENGTH = 20

# Report Configuration
REPORTS_FOLDER = "reports"
REPORTS_PATH = os.path.join(os.getcwd(), REPORTS_FOLDER)

# Ensure required directories exist
def ensure_directories():
    """Create required directories if they don't exist"""
    directories = [BACKUP_PATH, RESOURCES_PATH, REPORTS_PATH]
    
    for directory in directories:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
                print(f"Created directory: {directory}")
            except Exception as e:
                print(f"Error creating directory {directory}: {e}")

# Initialize directories when module is imported
ensure_directories()
