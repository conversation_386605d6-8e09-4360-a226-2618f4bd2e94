from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QTabWidget, QMenuBar, QStatusBar, QLabel, QPushButton,
                            QMessageBox, QFrame)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QAction, QIcon, QFont
from utils.translator import translator
from database.database_manager import DatabaseManager
from ui.products_widget import ProductsWidget
from ui.purchase_invoices_widget import PurchaseInvoicesWidget
from ui.sales_invoices_widget import SalesInvoicesWidget
from ui.reports_widget import ReportsWidget
from ui.settings_widget import SettingsWidget

class MainWindow(QMainWindow):
    logout_requested = pyqtSignal()
    
    def __init__(self, user_data):
        super().__init__()
        self.user_data = user_data
        self.db_manager = DatabaseManager()
        self.init_ui()
        self.apply_dark_theme()
    
    def init_ui(self):
        self.setWindowTitle(translator.tr("warehouse_management"))
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Header
        header_frame = self.create_header()
        main_layout.addWidget(header_frame)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setObjectName("mainTabWidget")
        
        # Add tabs based on user role
        self.setup_tabs()
        
        main_layout.addWidget(self.tab_widget)
        
        # Status bar
        self.setup_status_bar()
    
    def create_header(self):
        """Create header with company info and user info"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_frame.setFixedHeight(80)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 10, 20, 10)
        
        # Company info
        company_layout = QVBoxLayout()
        company_name = self.db_manager.get_setting("company_name") or "شركة إدارة المستودعات"
        company_label = QLabel(company_name)
        company_label.setObjectName("companyLabel")
        
        system_label = QLabel(translator.tr("warehouse_management"))
        system_label.setObjectName("systemLabel")
        
        company_layout.addWidget(company_label)
        company_layout.addWidget(system_label)
        
        header_layout.addLayout(company_layout)
        header_layout.addStretch()
        
        # User info and logout
        user_layout = QVBoxLayout()
        user_info = QLabel(f"المستخدم: {self.user_data.get('role', '')}")
        user_info.setObjectName("userLabel")
        
        logout_button = QPushButton(translator.tr("logout"))
        logout_button.setObjectName("logoutButton")
        logout_button.clicked.connect(self.handle_logout)
        
        user_layout.addWidget(user_info)
        user_layout.addWidget(logout_button)
        
        header_layout.addLayout(user_layout)
        
        return header_frame
    
    def setup_tabs(self):
        """Setup tabs based on user permissions"""
        role = self.user_data.get("role", "viewer")
        
        # Products tab - available for all roles
        self.products_widget = ProductsWidget(self.user_data)
        self.tab_widget.addTab(self.products_widget, translator.tr("products"))
        
        # Purchase invoices - admin and warehouse_staff only
        if role in ["admin", "warehouse_staff"]:
            self.purchase_widget = PurchaseInvoicesWidget(self.user_data)
            self.tab_widget.addTab(self.purchase_widget, translator.tr("purchase_invoices"))
            
            self.sales_widget = SalesInvoicesWidget(self.user_data)
            self.tab_widget.addTab(self.sales_widget, translator.tr("sales_invoices"))
        
        # Reports - available for all roles
        self.reports_widget = ReportsWidget(self.user_data)
        self.tab_widget.addTab(self.reports_widget, translator.tr("reports"))
        
        # Settings - admin only
        if role == "admin":
            self.settings_widget = SettingsWidget(self.user_data)
            self.tab_widget.addTab(self.settings_widget, translator.tr("settings"))
    
    def setup_status_bar(self):
        """Setup status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Add permanent widgets to status bar
        user_label = QLabel(f"المستخدم: {self.user_data.get('role', '')}")
        self.status_bar.addPermanentWidget(user_label)
        
        # Show ready message
        self.status_bar.showMessage("جاهز", 3000)
    
    def apply_dark_theme(self):
        """Apply dark theme styling"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            
            #headerFrame {
                background-color: #3c3c3c;
                border-bottom: 2px solid #4CAF50;
            }
            
            #companyLabel {
                font-size: 18px;
                font-weight: bold;
                color: #4CAF50;
            }
            
            #systemLabel {
                font-size: 12px;
                color: #cccccc;
            }
            
            #userLabel {
                font-size: 12px;
                color: #cccccc;
                text-align: right;
            }
            
            #logoutButton {
                padding: 8px 16px;
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
            }
            
            #logoutButton:hover {
                background-color: #da190b;
            }
            
            #mainTabWidget {
                background-color: #2b2b2b;
            }
            
            #mainTabWidget::pane {
                border: 1px solid #555555;
                background-color: #3c3c3c;
            }
            
            #mainTabWidget::tab-bar {
                alignment: right;
            }
            
            QTabBar::tab {
                background-color: #404040;
                color: #ffffff;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            
            QTabBar::tab:selected {
                background-color: #4CAF50;
                color: white;
            }
            
            QTabBar::tab:hover {
                background-color: #505050;
            }
            
            QStatusBar {
                background-color: #3c3c3c;
                color: #ffffff;
                border-top: 1px solid #555555;
            }
        """)
    
    def handle_logout(self):
        """Handle logout request"""
        reply = QMessageBox.question(
            self, 
            translator.tr("logout"),
            "هل أنت متأكد من تسجيل الخروج؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.logout_requested.emit()
            self.close()
    
    def closeEvent(self, event):
        """Handle window close event"""
        reply = QMessageBox.question(
            self,
            "إغلاق البرنامج",
            "هل أنت متأكد من إغلاق البرنامج؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            event.accept()
        else:
            event.ignore()
