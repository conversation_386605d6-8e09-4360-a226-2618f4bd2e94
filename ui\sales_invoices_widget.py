from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBox<PERSON>ayout, QGridLayout,
                            QLabel, QLineEdit, QPushButton, QComboBox, QTextEdit,
                            QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
                            QDateEdit, QFrame, QSpinBox, QDoubleSpinBox)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont
from database.database_manager import DatabaseManager
from utils.translator import translator
from datetime import datetime

class SalesInvoicesWidget(QWidget):
    def __init__(self, user_data):
        super().__init__()
        self.user_data = user_data
        self.db_manager = DatabaseManager()
        self.current_invoice_id = None
        self.invoice_items = []
        self.init_ui()
        self.apply_styling()
    
    def init_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Top section - Invoice header
        header_frame = self.create_invoice_header()
        main_layout.addWidget(header_frame)
        
        # Middle section - Items
        items_frame = self.create_items_section()
        main_layout.addWidget(items_frame)
        
        # Bottom section - Total and buttons
        bottom_frame = self.create_bottom_section()
        main_layout.addWidget(bottom_frame)
    
    def create_invoice_header(self):
        """Create invoice header section"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QGridLayout(header_frame)
        header_layout.setContentsMargins(20, 20, 20, 20)
        header_layout.setSpacing(15)
        
        # Title
        title_label = QLabel(translator.tr("sales_invoices"))
        title_label.setObjectName("sectionTitle")
        header_layout.addWidget(title_label, 0, 0, 1, 4)
        
        # Invoice number
        invoice_num_label = QLabel(translator.tr("invoice_number"))
        self.invoice_num_input = QLineEdit()
        self.invoice_num_input.setObjectName("inputField")
        self.generate_invoice_number()
        
        header_layout.addWidget(invoice_num_label, 1, 0)
        header_layout.addWidget(self.invoice_num_input, 1, 1)
        
        # Date
        date_label = QLabel(translator.tr("date"))
        self.date_input = QDateEdit()
        self.date_input.setObjectName("dateField")
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setCalendarPopup(True)
        
        header_layout.addWidget(date_label, 1, 2)
        header_layout.addWidget(self.date_input, 1, 3)
        
        # Customer
        customer_label = QLabel(translator.tr("customer"))
        self.customer_input = QLineEdit()
        self.customer_input.setObjectName("inputField")
        self.customer_input.setPlaceholderText("اسم العميل")
        
        header_layout.addWidget(customer_label, 2, 0)
        header_layout.addWidget(self.customer_input, 2, 1, 1, 3)
        
        return header_frame
    
    def create_items_section(self):
        """Create items section"""
        items_frame = QFrame()
        items_frame.setObjectName("itemsFrame")
        items_layout = QVBoxLayout(items_frame)
        items_layout.setContentsMargins(20, 20, 20, 20)
        
        # Add item section
        add_item_frame = QFrame()
        add_item_frame.setObjectName("addItemFrame")
        add_item_layout = QGridLayout(add_item_frame)
        add_item_layout.setSpacing(10)
        
        # Product selection
        product_label = QLabel("المنتج")
        self.product_combo = QComboBox()
        self.product_combo.setObjectName("comboBox")
        self.product_combo.currentTextChanged.connect(self.on_product_changed)
        self.load_products()
        
        add_item_layout.addWidget(product_label, 0, 0)
        add_item_layout.addWidget(self.product_combo, 0, 1)
        
        # Available quantity display
        self.available_qty_label = QLabel("المتوفر: 0")
        self.available_qty_label.setObjectName("availableQtyLabel")
        add_item_layout.addWidget(self.available_qty_label, 0, 2)
        
        # Quantity
        quantity_label = QLabel(translator.tr("quantity"))
        self.quantity_input = QDoubleSpinBox()
        self.quantity_input.setObjectName("spinBox")
        self.quantity_input.setMinimum(0.01)
        self.quantity_input.setMaximum(999999.99)
        self.quantity_input.setValue(1.0)
        
        add_item_layout.addWidget(quantity_label, 0, 3)
        add_item_layout.addWidget(self.quantity_input, 0, 4)
        
        # Unit price
        price_label = QLabel(translator.tr("unit_price"))
        self.price_input = QDoubleSpinBox()
        self.price_input.setObjectName("spinBox")
        self.price_input.setMinimum(0.01)
        self.price_input.setMaximum(999999.99)
        self.price_input.setValue(1.0)
        
        add_item_layout.addWidget(price_label, 0, 5)
        add_item_layout.addWidget(self.price_input, 0, 6)
        
        # Add button
        add_item_btn = QPushButton(translator.tr("add_item"))
        add_item_btn.setObjectName("primaryButton")
        add_item_btn.clicked.connect(self.add_item_to_invoice)
        
        add_item_layout.addWidget(add_item_btn, 0, 7)
        
        items_layout.addWidget(add_item_frame)
        
        # Items table
        self.items_table = QTableWidget()
        self.items_table.setObjectName("itemsTable")
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels([
            translator.tr("product_code"),
            translator.tr("product_name"),
            translator.tr("quantity"),
            translator.tr("unit_price"),
            translator.tr("total_price")
        ])
        
        # Configure table
        header = self.items_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        
        self.items_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.items_table.setAlternatingRowColors(True)
        
        # Add context menu for removing items
        self.items_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.items_table.customContextMenuRequested.connect(self.show_context_menu)
        
        items_layout.addWidget(self.items_table)
        
        return items_frame
    
    def create_bottom_section(self):
        """Create bottom section with total and buttons"""
        bottom_frame = QFrame()
        bottom_frame.setObjectName("bottomFrame")
        bottom_layout = QHBoxLayout(bottom_frame)
        bottom_layout.setContentsMargins(20, 20, 20, 20)
        
        # Total section
        total_frame = QFrame()
        total_layout = QVBoxLayout(total_frame)
        
        self.total_label = QLabel("الإجمالي: 0.00")
        self.total_label.setObjectName("totalLabel")
        total_layout.addWidget(self.total_label)
        
        bottom_layout.addWidget(total_frame)
        bottom_layout.addStretch()
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton(translator.tr("save_invoice"))
        save_btn.setObjectName("primaryButton")
        save_btn.clicked.connect(self.save_invoice)
        
        clear_btn = QPushButton(translator.tr("clear_fields"))
        clear_btn.setObjectName("secondaryButton")
        clear_btn.clicked.connect(self.clear_invoice)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(clear_btn)
        
        bottom_layout.addLayout(buttons_layout)
        
        return bottom_frame
    
    def load_products(self):
        """Load products with available quantities"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        language = self.db_manager.get_setting("language") or "ar"
        name_field = "name_ar" if language == "ar" else "name_en"
        
        cursor.execute(f"""
            SELECT id, code, {name_field}, quantity 
            FROM products 
            WHERE quantity > 0 
            ORDER BY {name_field}
        """)
        products = cursor.fetchall()
        
        self.product_combo.clear()
        self.product_quantities = {}
        
        for product_id, code, name, quantity in products:
            self.product_combo.addItem(f"{code} - {name}", product_id)
            self.product_quantities[product_id] = quantity
        
        conn.close()
        
        # Update available quantity display
        if self.product_combo.count() > 0:
            self.on_product_changed()
    
    def on_product_changed(self):
        """Update available quantity when product changes"""
        product_id = self.product_combo.currentData()
        if product_id and product_id in self.product_quantities:
            available_qty = self.product_quantities[product_id]
            self.available_qty_label.setText(f"المتوفر: {available_qty}")
            self.quantity_input.setMaximum(available_qty)
        else:
            self.available_qty_label.setText("المتوفر: 0")
            self.quantity_input.setMaximum(0)
    
    def generate_invoice_number(self):
        """Generate new invoice number"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        # Get last invoice number
        cursor.execute("SELECT MAX(id) FROM sales_invoices")
        result = cursor.fetchone()
        last_id = result[0] if result[0] else 0
        
        # Generate new number
        new_number = f"SAL{datetime.now().strftime('%Y%m%d')}{last_id + 1:04d}"
        self.invoice_num_input.setText(new_number)
        
        conn.close()
    
    def add_item_to_invoice(self):
        """Add item to invoice"""
        if self.product_combo.currentData() is None:
            QMessageBox.warning(self, translator.tr("warning"), "يرجى اختيار منتج")
            return
        
        product_id = self.product_combo.currentData()
        product_text = self.product_combo.currentText()
        quantity = self.quantity_input.value()
        unit_price = self.price_input.value()
        
        # Check available quantity
        available_qty = self.product_quantities.get(product_id, 0)
        if quantity > available_qty:
            QMessageBox.warning(self, translator.tr("warning"), 
                              f"الكمية المطلوبة ({quantity}) أكبر من المتوفر ({available_qty})")
            return
        
        total_price = quantity * unit_price
        
        # Get product code and name
        parts = product_text.split(" - ")
        product_code = parts[0] if len(parts) > 0 else ""
        product_name = parts[1] if len(parts) > 1 else ""
        
        # Add to table
        row = self.items_table.rowCount()
        self.items_table.insertRow(row)
        
        self.items_table.setItem(row, 0, QTableWidgetItem(product_code))
        self.items_table.setItem(row, 1, QTableWidgetItem(product_name))
        self.items_table.setItem(row, 2, QTableWidgetItem(str(quantity)))
        self.items_table.setItem(row, 3, QTableWidgetItem(str(unit_price)))
        self.items_table.setItem(row, 4, QTableWidgetItem(str(total_price)))
        
        # Store item data
        self.invoice_items.append({
            'product_id': product_id,
            'quantity': quantity,
            'unit_price': unit_price,
            'total_price': total_price
        })
        
        # Update available quantity
        self.product_quantities[product_id] -= quantity
        self.on_product_changed()
        
        # Update total
        self.update_total()
        
        # Reset inputs
        self.quantity_input.setValue(1.0)
        self.price_input.setValue(1.0)
    
    def show_context_menu(self, position):
        """Show context menu for table"""
        from PyQt6.QtWidgets import QMenu
        from PyQt6.QtGui import QAction
        
        if self.items_table.itemAt(position):
            menu = QMenu(self)
            
            remove_action = QAction("حذف العنصر", self)
            remove_action.triggered.connect(self.remove_selected_item)
            menu.addAction(remove_action)
            
            menu.exec(self.items_table.mapToGlobal(position))
    
    def remove_selected_item(self):
        """Remove selected item from invoice"""
        current_row = self.items_table.currentRow()
        if current_row >= 0:
            # Restore quantity to available stock
            item = self.invoice_items[current_row]
            product_id = item['product_id']
            quantity = item['quantity']
            
            if product_id in self.product_quantities:
                self.product_quantities[product_id] += quantity
            
            # Remove from table and list
            self.items_table.removeRow(current_row)
            self.invoice_items.pop(current_row)
            
            # Update displays
            self.on_product_changed()
            self.update_total()
    
    def update_total(self):
        """Update invoice total"""
        total = sum(item['total_price'] for item in self.invoice_items)
        self.total_label.setText(f"الإجمالي: {total:.2f}")
    
    def save_invoice(self):
        """Save sales invoice"""
        if not self.validate_invoice():
            return
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            # Calculate total
            total_amount = sum(item['total_price'] for item in self.invoice_items)
            
            # Insert invoice
            cursor.execute("""
                INSERT INTO sales_invoices (invoice_number, customer_name, invoice_date, 
                                          total_amount, created_by)
                VALUES (?, ?, ?, ?, ?)
            """, (
                self.invoice_num_input.text(),
                self.customer_input.text(),
                self.date_input.date().toString("yyyy-MM-dd"),
                total_amount,
                self.user_data["user_id"]
            ))
            
            invoice_id = cursor.lastrowid
            
            # Insert invoice items and update product quantities
            for item in self.invoice_items:
                # Insert item
                cursor.execute("""
                    INSERT INTO sales_invoice_items (invoice_id, product_id, quantity, 
                                                   unit_price, total_price)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    invoice_id,
                    item['product_id'],
                    item['quantity'],
                    item['unit_price'],
                    item['total_price']
                ))
                
                # Update product quantity (decrease)
                cursor.execute("""
                    UPDATE products 
                    SET quantity = quantity - ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (item['quantity'], item['product_id']))
            
            conn.commit()
            QMessageBox.information(self, translator.tr("success"), "تم حفظ فاتورة البيع بنجاح")
            self.clear_invoice()
            
        except Exception as e:
            conn.rollback()
            QMessageBox.critical(self, translator.tr("error"), f"خطأ في حفظ الفاتورة: {str(e)}")
        finally:
            conn.close()
    
    def validate_invoice(self):
        """Validate invoice data"""
        if not self.invoice_num_input.text().strip():
            QMessageBox.warning(self, translator.tr("warning"), "يرجى إدخال رقم الفاتورة")
            return False
        
        if not self.customer_input.text().strip():
            QMessageBox.warning(self, translator.tr("warning"), "يرجى إدخال اسم العميل")
            return False
        
        if not self.invoice_items:
            QMessageBox.warning(self, translator.tr("warning"), "يرجى إضافة عناصر للفاتورة")
            return False
        
        return True
    
    def clear_invoice(self):
        """Clear invoice form"""
        self.generate_invoice_number()
        self.date_input.setDate(QDate.currentDate())
        self.customer_input.clear()
        self.items_table.setRowCount(0)
        self.invoice_items.clear()
        self.update_total()
        self.quantity_input.setValue(1.0)
        self.price_input.setValue(1.0)
        
        # Reload products to reset quantities
        self.load_products()
    
    def apply_styling(self):
        """Apply widget styling"""
        self.setStyleSheet("""
            #headerFrame, #itemsFrame, #bottomFrame, #addItemFrame {
                background-color: #3c3c3c;
                border-radius: 8px;
                border: 1px solid #555555;
                margin: 5px;
            }
            
            #sectionTitle {
                font-size: 18px;
                font-weight: bold;
                color: #4CAF50;
                margin-bottom: 15px;
            }
            
            #totalLabel {
                font-size: 16px;
                font-weight: bold;
                color: #4CAF50;
            }
            
            #availableQtyLabel {
                font-size: 12px;
                color: #FFC107;
                font-weight: bold;
            }
            
            #inputField, #dateField {
                padding: 8px;
                border: 1px solid #555555;
                border-radius: 4px;
                background-color: #404040;
                color: #ffffff;
                min-height: 20px;
            }
            
            #inputField:focus, #dateField:focus {
                border-color: #4CAF50;
            }
            
            #comboBox {
                padding: 8px;
                border: 1px solid #555555;
                border-radius: 4px;
                background-color: #404040;
                color: #ffffff;
                min-height: 20px;
            }
            
            #spinBox {
                padding: 8px;
                border: 1px solid #555555;
                border-radius: 4px;
                background-color: #404040;
                color: #ffffff;
                min-height: 20px;
            }
            
            #primaryButton {
                padding: 10px 20px;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                min-height: 20px;
            }
            
            #primaryButton:hover {
                background-color: #45a049;
            }
            
            #secondaryButton {
                padding: 10px 20px;
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                min-height: 20px;
            }
            
            #secondaryButton:hover {
                background-color: #1976D2;
            }
            
            #itemsTable {
                background-color: #404040;
                alternate-background-color: #454545;
                selection-background-color: #4CAF50;
                gridline-color: #555555;
                color: #ffffff;
            }
            
            #itemsTable::item {
                padding: 8px;
            }
            
            QHeaderView::section {
                background-color: #505050;
                color: #ffffff;
                padding: 8px;
                border: 1px solid #555555;
                font-weight: bold;
            }
        """)
