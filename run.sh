#!/bin/bash

echo "========================================"
echo "    نظام إدارة فواتير المستودعات"
echo "========================================"
echo ""
echo "جاري تشغيل البرنامج..."
echo ""

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "خطأ: Python3 غير مثبت على النظام"
    echo "يرجى تثبيت Python 3.8 أو أحدث"
    exit 1
fi

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "خطأ: pip3 غير مثبت على النظام"
    echo "يرجى تثبيت pip3"
    exit 1
fi

# Check if virtual environment exists, create if not
if [ ! -d "venv" ]; then
    echo "إنشاء بيئة افتراضية..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "تفعيل البيئة الافتراضية..."
source venv/bin/activate

# Install requirements
echo "جاري التحقق من المتطلبات..."
pip install -r requirements.txt

# Run the application
echo ""
echo "بدء تشغيل البرنامج..."
echo ""
echo "بيانات الدخول الافتراضية:"
echo "اسم المستخدم: admin"
echo "كلمة المرور: admin123"
echo ""

python3 main.py

echo ""
echo "تم إغلاق البرنامج"
