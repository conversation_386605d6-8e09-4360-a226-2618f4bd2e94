import sys
from PyQt6.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QMessageBox, QFrame)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPalette, QColor
from database.database_manager import DatabaseManager
from utils.translator import translator

class LoginWindow(QWidget):
    login_successful = pyqtSignal(dict)  # Signal to emit user data on successful login
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.init_ui()
        self.apply_dark_theme()
    
    def init_ui(self):
        self.setWindowTitle(translator.tr("warehouse_management"))
        self.setFixedSize(400, 300)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # Create main frame
        main_frame = QFrame()
        main_frame.setObjectName("mainFrame")
        frame_layout = QVBoxLayout(main_frame)
        frame_layout.setContentsMargins(40, 40, 40, 40)
        frame_layout.setSpacing(20)
        
        # Title
        title_label = QLabel(translator.tr("warehouse_management"))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setObjectName("titleLabel")
        frame_layout.addWidget(title_label)
        
        # Login form
        form_layout = QVBoxLayout()
        form_layout.setSpacing(15)
        
        # Username
        username_label = QLabel(translator.tr("username"))
        username_label.setObjectName("fieldLabel")
        self.username_input = QLineEdit()
        self.username_input.setObjectName("inputField")
        self.username_input.setPlaceholderText(translator.tr("username"))
        
        form_layout.addWidget(username_label)
        form_layout.addWidget(self.username_input)
        
        # Password
        password_label = QLabel(translator.tr("password"))
        password_label.setObjectName("fieldLabel")
        self.password_input = QLineEdit()
        self.password_input.setObjectName("inputField")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setPlaceholderText(translator.tr("password"))
        
        form_layout.addWidget(password_label)
        form_layout.addWidget(self.password_input)
        
        frame_layout.addLayout(form_layout)
        
        # Login button
        self.login_button = QPushButton(translator.tr("login_button"))
        self.login_button.setObjectName("loginButton")
        self.login_button.clicked.connect(self.handle_login)
        frame_layout.addWidget(self.login_button)
        
        # Close button
        close_button = QPushButton("×")
        close_button.setObjectName("closeButton")
        close_button.setFixedSize(30, 30)
        close_button.clicked.connect(self.close)
        
        # Position close button
        close_layout = QHBoxLayout()
        close_layout.addStretch()
        close_layout.addWidget(close_button)
        close_layout.setContentsMargins(10, 10, 10, 0)
        
        main_layout.addLayout(close_layout)
        main_layout.addWidget(main_frame)
        
        self.setLayout(main_layout)
        
        # Connect Enter key to login
        self.username_input.returnPressed.connect(self.handle_login)
        self.password_input.returnPressed.connect(self.handle_login)
        
        # Set default values for testing
        self.username_input.setText("admin")
        self.password_input.setText("admin123")
    
    def apply_dark_theme(self):
        """Apply dark theme styling"""
        self.setStyleSheet("""
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            #mainFrame {
                background-color: #3c3c3c;
                border-radius: 10px;
                border: 1px solid #555555;
            }
            
            #titleLabel {
                font-size: 24px;
                font-weight: bold;
                color: #4CAF50;
                margin-bottom: 20px;
            }
            
            #fieldLabel {
                font-size: 12px;
                color: #cccccc;
                margin-bottom: 5px;
            }
            
            #inputField {
                padding: 12px;
                border: 2px solid #555555;
                border-radius: 6px;
                background-color: #404040;
                color: #ffffff;
                font-size: 14px;
            }
            
            #inputField:focus {
                border-color: #4CAF50;
                background-color: #454545;
            }
            
            #loginButton {
                padding: 12px;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 16px;
                font-weight: bold;
            }
            
            #loginButton:hover {
                background-color: #45a049;
            }
            
            #loginButton:pressed {
                background-color: #3d8b40;
            }
            
            #closeButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 15px;
                font-size: 18px;
                font-weight: bold;
            }
            
            #closeButton:hover {
                background-color: #da190b;
            }
        """)
    
    def handle_login(self):
        """Handle login attempt"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(self, translator.tr("warning"), 
                              "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # Verify credentials
        user_data = self.db_manager.verify_password(username, password)
        
        if user_data:
            # Update last login
            self.db_manager.update_last_login(user_data["user_id"])
            
            # Emit signal with user data
            self.login_successful.emit(user_data)
            self.close()
        else:
            QMessageBox.critical(self, translator.tr("error"), 
                               translator.tr("invalid_credentials"))
            self.password_input.clear()
            self.password_input.setFocus()
    
    def center_on_screen(self):
        """Center the window on screen"""
        screen = self.screen().availableGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
