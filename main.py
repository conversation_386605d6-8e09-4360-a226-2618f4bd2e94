#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QIcon

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.database_manager import DatabaseManager
from ui.login_window import LoginWindow
from ui.main_window import MainWindow
from utils.translator import translator

class WarehouseApp:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.setup_application()
        self.db_manager = DatabaseManager()
        self.main_window = None
        self.login_window = None
        
        # Load language setting
        language = self.db_manager.get_setting("language") or "ar"
        translator.set_language(language)
        
        self.show_login()
    
    def setup_application(self):
        """Setup application properties"""
        self.app.setApplicationName("نظام إدارة المستودعات")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("شركة إدارة المستودعات")
        
        # Set application font
        font = QFont("Segoe UI", 10)
        self.app.setFont(font)
        
        # Set RTL layout for Arabic
        self.app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # Set application style
        self.app.setStyle('Fusion')
    
    def show_login(self):
        """Show login window"""
        self.login_window = LoginWindow()
        self.login_window.login_successful.connect(self.on_login_successful)
        self.login_window.center_on_screen()
        self.login_window.show()
    
    def on_login_successful(self, user_data):
        """Handle successful login"""
        try:
            self.main_window = MainWindow(user_data)
            self.main_window.logout_requested.connect(self.on_logout_requested)
            self.main_window.show()
            
            # Close login window
            if self.login_window:
                self.login_window.close()
                
        except Exception as e:
            QMessageBox.critical(None, "خطأ", f"خطأ في فتح النافذة الرئيسية: {str(e)}")
            self.show_login()
    
    def on_logout_requested(self):
        """Handle logout request"""
        if self.main_window:
            self.main_window.close()
            self.main_window = None
        
        self.show_login()
    
    def run(self):
        """Run the application"""
        try:
            return self.app.exec()
        except Exception as e:
            QMessageBox.critical(None, "خطأ فادح", f"خطأ في تشغيل التطبيق: {str(e)}")
            return 1

def main():
    """Main entry point"""
    try:
        # Create application instance
        app = WarehouseApp()
        
        # Run application
        sys.exit(app.run())
        
    except Exception as e:
        print(f"خطأ في بدء التطبيق: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
