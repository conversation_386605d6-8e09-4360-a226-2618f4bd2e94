# نظام إدارة فواتير المستودعات

برنامج متكامل لإدارة فواتير المستودعات باستخدام Python و PyQt6 مع دعم اللغتين العربية والإنجليزية.

## المميزات

### 1. صلاحيات المستخدمين
- تسجيل دخول آمن
- ثلاثة أدوار: مدير، موظف مخزن، مشاهد فقط
- إدارة المستخدمين (للمدير فقط)

### 2. إدارة المنتجات
- كود المنتج (يدوي أو تلقائي)
- توليد باركود عشوائي
- إدارة وحدات القياس
- إدارة حالات المواد
- إدارة التصنيفات
- دعم الصور
- البحث والتصفية

### 3. فواتير الشراء
- إنشاء فواتير شراء
- إضافة أصناف متعددة
- تحديث المخزون تلقائياً
- حفظ بيانات الموردين

### 4. فواتير البيع
- إنشاء فواتير بيع
- التحقق من الكمية المتوفرة
- تقليل المخزون تلقائياً
- حفظ بيانات العملاء

### 5. التقارير
- تقارير المشتريات
- تقارير المبيعات
- تقارير المخزون
- تصدير PDF و Excel
- تصفية حسب التاريخ والعميل/المورد

### 6. الإعدادات
- تغيير اللغة (عربي/إنجليزي)
- إعدادات الشركة والشعار
- النسخ الاحتياطي والاستعادة
- إدارة المستخدمين

## متطلبات النظام

- Python 3.8 أو أحدث
- Windows 10/11 (يمكن تشغيله على Linux/Mac مع تعديلات بسيطة)

## التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. تشغيل البرنامج

```bash
python main.py
```

### 3. بيانات الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123
- **الدور:** مدير

## هيكل المشروع

```
warehouse_management/
├── main.py                     # نقطة البداية الرئيسية
├── requirements.txt            # المتطلبات
├── README.md                  # هذا الملف
├── database/
│   └── database_manager.py    # إدارة قاعدة البيانات
├── ui/
│   ├── login_window.py        # نافذة تسجيل الدخول
│   ├── main_window.py         # النافذة الرئيسية
│   ├── products_widget.py     # واجهة المنتجات
│   ├── purchase_invoices_widget.py  # واجهة فواتير الشراء
│   ├── sales_invoices_widget.py     # واجهة فواتير البيع
│   ├── reports_widget.py      # واجهة التقارير
│   └── settings_widget.py     # واجهة الإعدادات
├── utils/
│   ├── translator.py          # نظام الترجمة
│   └── barcode_generator.py   # مولد الباركود
└── resources/                 # الموارد (الصور، الشعارات)
```

## قاعدة البيانات

يستخدم البرنامج قاعدة بيانات SQLite مع الجداول التالية:

- **users** - المستخدمين والصلاحيات
- **products** - المنتجات
- **units** - وحدات القياس
- **categories** - التصنيفات
- **item_status** - حالات المواد
- **purchase_invoices** - فواتير الشراء
- **purchase_invoice_items** - أصناف فواتير الشراء
- **sales_invoices** - فواتير البيع
- **sales_invoice_items** - أصناف فواتير البيع
- **settings** - إعدادات النظام

## الواجهة

- **تصميم عصري غامق** مع ألوان احترافية
- **دعم كامل للغة العربية** مع اتجاه النص من اليمين لليسار
- **واجهة سهلة الاستخدام** مع تبويبات منظمة
- **رسائل تأكيد وتحذير** باللغة المناسبة

## الأمان

- **تشفير كلمات المرور** باستخدام bcrypt
- **صلاحيات متدرجة** حسب دور المستخدم
- **تسجيل العمليات** مع معرف المستخدم والوقت

## النسخ الاحتياطي

- **نسخ احتياطي يدوي** في أي وقت
- **استعادة من ملف** احتياطي
- **إعدادات النسخ التلقائي** (يومي/أسبوعي/شهري)

## التطوير المستقبلي

- [ ] دعم الشبكة المحلية (LAN)
- [ ] تقارير أكثر تفصيلاً
- [ ] إشعارات نفاد المخزون
- [ ] طباعة الفواتير
- [ ] ربط مع أجهزة الباركود
- [ ] API للتكامل مع أنظمة أخرى

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

## الترخيص

هذا البرنامج مفتوح المصدر ومتاح للاستخدام والتطوير.

---

**ملاحظة:** هذا البرنامج تم تطويره باستخدام أحدث التقنيات ويدعم التشغيل على أنظمة Windows بشكل كامل. للتشغيل على أنظمة أخرى قد تحتاج لتعديلات بسيطة في مسارات الملفات.
